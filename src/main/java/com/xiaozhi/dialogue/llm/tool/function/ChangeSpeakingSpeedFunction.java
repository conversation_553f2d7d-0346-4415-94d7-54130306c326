package com.xiaozhi.dialogue.llm.tool.function;

import com.xiaozhi.communication.common.ChatSession;
import com.xiaozhi.dialogue.llm.ChatService;
import com.xiaozhi.dialogue.llm.tool.ToolCallStringResultConverter;
import com.xiaozhi.dialogue.llm.tool.ToolsGlobalRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.ai.tool.metadata.ToolMetadata;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
public class ChangeSpeakingSpeedFunction implements ToolsGlobalRegistry.GlobalFunction {

    ToolCallback toolCallback = FunctionToolCallback
            .builder("func_change_speaking_speed", (Map<String, Double> params, ToolContext context) -> {
                var session = (ChatSession) context.getContext().get(ChatService.TOOL_CONTEXT_SESSION_KEY);
                var currentRole = session.getCurrentRole();

                if (currentRole == null) {
                    return "尚未配置角色，无法调整语速";
                }

                var speed = params.getOrDefault("speed", 1.0);
                currentRole.setSpeed(speed);

                return STR."语速已调整至\{speed}";
            })
            .toolMetadata(ToolMetadata.builder().returnDirect(false).build())
            .description("Provides change speaking speed function, need the speed param")
            .inputSchema("""
                    {
                        "type": "object",
                        "properties": {
                            "speed": {
                                "type": "number",
                                "description": "Speed range: 0.5–2.0 (default 1.0), adjustable in 0.1 steps."
                            }
                        },
                        "required": ["speed"]
                    }
                    """)
            .inputType(Map.class)
            .toolCallResultConverter(ToolCallStringResultConverter.INSTANCE)
            .build();

    @Override
    public ToolCallback getFunctionCallTool(ChatSession chatSession) {
        return toolCallback;
    }
}
